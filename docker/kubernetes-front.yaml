# ------------------- 日志服务 Deployment ------------------- #

apiVersion: apps/v1beta2
kind: Deployment
metadata:
  labels:
    app: autostudio-front-server
  name: autostudio-front-server
  # 集群环境的空间名称
  namespace: autostudio-sit
spec:
  replicas: 1
  revisionHistoryLimit: 10
  selector:
    matchLabels:
      app: autostudio-front-server
  template:
    metadata:
      labels:
        app: autostudio-front-server
    spec:
      containers:
      - name: autostudio-front-server
        imagePullPolicy: Always
        # 日志服务镜像仓库地址
        image: 192.168.1.119/ms-autostudio/autostudio-front:sit
        ports:
        - containerPort: 8080
          protocol: TCP
        env:
          - name: SERVERHTTP
            value: "http://autostudio-zuul-server-svc:8769"
   
---
# ------------------- 日志服务 Service ------------------- #

apiVersion: v1
kind: Service
metadata:
  labels:
    app: autostudio-front-server
  name: autostudio-front-server-svc
  # 集群环境的空间名称
  namespace: autostudio-sit
spec:
  ports:
  - nodePort: 30603
    port: 8080
    protocol: TCP
    targetPort: 8080
  selector:
    app: autostudio-front-server
  type: NodePort
status:
  loadBalancer: {}