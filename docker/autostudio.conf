server {
    listen                      8080; 
    server_name                 localhost;
    client_max_body_size        500m;
    
    add_header Cache-Control    no-cache;

    location / {
        root    /home/<USER>
        try_files $uri /index.html;
        index   index.html index.php;
    }
	
	location /api- {
            add_header 'Access-Control-Allow-Origin' '*';
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header REMOTE-HOST $remote_addr;
            proxy_set_header X-Real-Port $remote_port;
            proxy_set_header X-Forwarded-For $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_headers_hash_max_size 512;
            proxy_headers_hash_bucket_size 128;
            proxy_set_header X-Forwarded-For $http_x_forwarded_for;
            rewrite  ^/api/(.*)$ /$1 break;
            proxy_connect_timeout 1200000s;
            proxy_read_timeout 360000s;
            proxy_send_timeout 120000s;
    }

    location /api {
        if ($request_method = 'OPTIONS') {
            add_header 'Access-Control-Allow-Origin' '*';
            add_header 'Access-Control-Allow-Methods' 'GET, POST, OPTIONS';
            add_header 'Access-Control-Allow-Headers' 'DNT,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Range,c-app-id,c-business-id,c-dynamic-password-foruser,c-tenancy-id,c-timestamp,c-token';
            return 204;
        }
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header REMOTE-HOST $remote_addr;  
        proxy_set_header X-Real-Port $remote_port;
        proxy_set_header X-Forwarded-For $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_headers_hash_max_size 512;
        proxy_headers_hash_bucket_size 128;
        proxy_set_header X-Forwarded-For $http_x_forwarded_for;
        
        proxy_pass serverurl;
        proxy_connect_timeout 1200000s;
        proxy_read_timeout 360000s;
        proxy_send_timeout 120000s;
    }   
}
