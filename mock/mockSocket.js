const Mock = require('mockjs')

module.exports = [
  {
    url: '/mockWebsocket',
    type: 'get',
    response: config => {
      const data = Mock.mock({
        'data|3': [
          {
            appId: '@id',
            openId: /sisf|dsdf|anb2|sss4|sd22|bbas|sss/,
            'msgId|1-100000': 1,
            content: '@csentence',
            sendType: /REC|SEND/,
            'createTime|1543800000-11543851602': 1,
            msgType: 'text'
          }
        ]
      })
      return {
        code: 20000,
        data: data.data
      }
    }
  }
]
