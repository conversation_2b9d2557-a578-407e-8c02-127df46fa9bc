const Mock = require('mockjs')

const datas = Mock.mock({
  'items': [{
    "id": "1a6a58b1-5a86-4d51-935b-e59996b3d678",
		"pid": "0",
		"nodeType": "spine",
		"modelType": "chart",
		"modelInnerType": "group",
		"authType": "source",
		"createBy": "demo",
		"level": 0,
		"mode": 0,
		"dataSourceId": "0",
		"name": "测试数据",
		"label": "1",
		"privileges": "grant,manage,use",
    "children": [{
      "id": "1d06e2a0-d936-4192-b523-2eb1e8cebd51",
      "pid": "bfa7d87f-c76f-4406-9f19-0adccb7c568d",
      "nodeType": "leaf",
      "modelType": "chart",
      "modelInnerType": "bar",
      "authType": "source",
      "createBy": "admin",
      "level": 0,
      "mode": 0,
      "dataSourceId": "0",
      "name": "夺金项目排行榜",
      "label": "夺金项目排行榜",
      "privileges": "use",
      "children": null,
      "allLeafs": 0
    }]
  },{
		"id": "3f551269-d985-4633-884d-d118704da2db",
		"pid": "0",
		"nodeType": "spine",
		"modelType": "chart",
		"modelInnerType": "group",
		"authType": "source",
		"createBy": "admin",
		"level": 0,
		"mode": 0,
		"dataSourceId": "0",
		"name": "【官方示例】",
		"label": "【官方示例】",
		"privileges": "use",
		"children": [{
			"id": "bfa7d87f-c76f-4406-9f19-0adccb7c568d",
			"pid": "3f551269-d985-4633-884d-d118704da2db",
			"nodeType": "spine",
			"modelType": "chart",
			"modelInnerType": "group",
			"authType": "source",
			"createBy": "admin",
			"level": 0,
			"mode": 0,
			"dataSourceId": "0",
			"name": "东京奥运会",
			"label": "东京奥运会",
			"privileges": "use",
			"children": [{
				"id": "1d06e2a0-d936-4192-b523-2eb1e8cebd51",
				"pid": "bfa7d87f-c76f-4406-9f19-0adccb7c568d",
				"nodeType": "leaf",
				"modelType": "chart",
				"modelInnerType": "bar",
				"authType": "source",
				"createBy": "admin",
				"level": 0,
				"mode": 0,
				"dataSourceId": "0",
				"name": "夺金项目排行榜",
				"label": "夺金项目排行榜",
				"privileges": "use",
				"children": null,
				"allLeafs": 0
			}, {
				"id": "95f8e3a2-62a5-48a7-a719-fcf53746da8d",
				"pid": "bfa7d87f-c76f-4406-9f19-0adccb7c568d",
				"nodeType": "leaf",
				"modelType": "chart",
				"modelInnerType": "pie-rose",
				"authType": "source",
				"createBy": "admin",
				"level": 0,
				"mode": 0,
				"dataSourceId": "0",
				"name": "观众年龄",
				"label": "观众年龄",
				"privileges": "use",
				"children": null,
				"allLeafs": 0
			}, {
				"id": "8a26a936-89bf-45a8-b1ce-d5ef1719465d",
				"pid": "bfa7d87f-c76f-4406-9f19-0adccb7c568d",
				"nodeType": "leaf",
				"modelType": "chart",
				"modelInnerType": "pie",
				"authType": "source",
				"createBy": "admin",
				"level": 0,
				"mode": 0,
				"dataSourceId": "0",
				"name": "观众性别",
				"label": "观众性别",
				"privileges": "use",
				"children": null,
				"allLeafs": 0
			}, {
				"id": "3f201733-bbb3-485e-a1d6-0fe4f00b5304",
				"pid": "bfa7d87f-c76f-4406-9f19-0adccb7c568d",
				"nodeType": "leaf",
				"modelType": "chart",
				"modelInnerType": "bar-horizontal",
				"authType": "source",
				"createBy": "admin",
				"level": 0,
				"mode": 0,
				"dataSourceId": "0",
				"name": "金牌榜",
				"label": "金牌榜",
				"privileges": "use",
				"children": null,
				"allLeafs": 0
			}, {
				"id": "0de1d446-8300-4ab3-a4ef-4e8f8579cb2e",
				"pid": "bfa7d87f-c76f-4406-9f19-0adccb7c568d",
				"nodeType": "leaf",
				"modelType": "chart",
				"modelInnerType": "text",
				"authType": "source",
				"createBy": "admin",
				"level": 0,
				"mode": 0,
				"dataSourceId": "0",
				"name": "静态时间",
				"label": "静态时间",
				"privileges": "use",
				"children": null,
				"allLeafs": 0
			}, {
				"id": "aff5be0c-f195-4fce-bd2b-b8d0e63764de",
				"pid": "bfa7d87f-c76f-4406-9f19-0adccb7c568d",
				"nodeType": "leaf",
				"modelType": "chart",
				"modelInnerType": "bar-horizontal",
				"authType": "source",
				"createBy": "admin",
				"level": 0,
				"mode": 0,
				"dataSourceId": "0",
				"name": "铜牌榜",
				"label": "铜牌榜",
				"privileges": "use",
				"children": null,
				"allLeafs": 0
			}, {
				"id": "692d5bdc-aa70-4fce-b830-b8d6620539c6",
				"pid": "bfa7d87f-c76f-4406-9f19-0adccb7c568d",
				"nodeType": "leaf",
				"modelType": "chart",
				"modelInnerType": "bar-horizontal",
				"authType": "source",
				"createBy": "admin",
				"level": 0,
				"mode": 0,
				"dataSourceId": "0",
				"name": "银牌榜",
				"label": "银牌榜",
				"privileges": "use",
				"children": null,
				"allLeafs": 0
			}, {
				"id": "cb66836d-a34c-40c6-87e7-0db0375ec19e",
				"pid": "bfa7d87f-c76f-4406-9f19-0adccb7c568d",
				"nodeType": "leaf",
				"modelType": "chart",
				"modelInnerType": "line",
				"authType": "source",
				"createBy": "admin",
				"level": 0,
				"mode": 0,
				"dataSourceId": "0",
				"name": "中国夺金趋势榜",
				"label": "中国夺金趋势榜",
				"privileges": "use",
				"children": null,
				"allLeafs": 0
			}, {
				"id": "8d1c3f30-0639-452e-9883-164f37353324",
				"pid": "bfa7d87f-c76f-4406-9f19-0adccb7c568d",
				"nodeType": "leaf",
				"modelType": "chart",
				"modelInnerType": "text",
				"authType": "source",
				"createBy": "admin",
				"level": 0,
				"mode": 0,
				"dataSourceId": "0",
				"name": "中国奖牌总数",
				"label": "中国奖牌总数",
				"privileges": "use",
				"children": null,
				"allLeafs": 0
			}, {
				"id": "175b25df-1939-4582-a9c5-d9e8ed3ea2b1",
				"pid": "bfa7d87f-c76f-4406-9f19-0adccb7c568d",
				"nodeType": "leaf",
				"modelType": "chart",
				"modelInnerType": "text",
				"authType": "source",
				"createBy": "admin",
				"level": 0,
				"mode": 0,
				"dataSourceId": "0",
				"name": "中国金牌总数",
				"label": "中国金牌总数",
				"privileges": "use",
				"children": null,
				"allLeafs": 0
			}, {
				"id": "da18eecd-feff-4140-a291-cce4abf1afaa",
				"pid": "bfa7d87f-c76f-4406-9f19-0adccb7c568d",
				"nodeType": "leaf",
				"modelType": "chart",
				"modelInnerType": "text",
				"authType": "source",
				"createBy": "admin",
				"level": 0,
				"mode": 0,
				"dataSourceId": "0",
				"name": "中国铜牌总数",
				"label": "中国铜牌总数",
				"privileges": "use",
				"children": null,
				"allLeafs": 0
			}, {
				"id": "c3da496f-073c-413a-bebd-e7f1a4a00ba7",
				"pid": "bfa7d87f-c76f-4406-9f19-0adccb7c568d",
				"nodeType": "leaf",
				"modelType": "chart",
				"modelInnerType": "text",
				"authType": "source",
				"createBy": "admin",
				"level": 0,
				"mode": 0,
				"dataSourceId": "0",
				"name": "中国银牌总数",
				"label": "中国银牌总数",
				"privileges": "use",
				"children": null,
				"allLeafs": 0
			}],
			"allLeafs": 0
		}, {
			"id": "2c200620-f2f3-4224-a41d-381fa061591b",
			"pid": "3f551269-d985-4633-884d-d118704da2db",
			"nodeType": "spine",
			"modelType": "chart",
			"modelInnerType": "group",
			"authType": "source",
			"createBy": "admin",
			"level": 0,
			"mode": 0,
			"dataSourceId": "0",
			"name": "某运动品牌销售数据分析",
			"label": "某运动品牌销售数据分析",
			"privileges": "use",
			"children": [{
				"id": "c52b6d95-b404-4130-8635-5903cb8d0e84",
				"pid": "2c200620-f2f3-4224-a41d-381fa061591b",
				"nodeType": "leaf",
				"modelType": "chart",
				"modelInnerType": "funnel",
				"authType": "source",
				"createBy": "admin",
				"level": 0,
				"mode": 0,
				"dataSourceId": "0",
				"name": "当年各部门销量",
				"label": "当年各部门销量",
				"privileges": "use",
				"children": null,
				"allLeafs": 0
			}, {
				"id": "4242cbb0-fca4-4b27-b2a7-ca576a18815e",
				"pid": "2c200620-f2f3-4224-a41d-381fa061591b",
				"nodeType": "leaf",
				"modelType": "chart",
				"modelInnerType": "pie",
				"authType": "source",
				"createBy": "admin",
				"level": 0,
				"mode": 0,
				"dataSourceId": "0",
				"name": "当年各产品销量",
				"label": "当年各产品销量",
				"privileges": "use",
				"children": null,
				"allLeafs": 0
			}, {
				"id": "8412d80d-1830-4128-bc6a-019cf32afc7f",
				"pid": "2c200620-f2f3-4224-a41d-381fa061591b",
				"nodeType": "leaf",
				"modelType": "chart",
				"modelInnerType": "line",
				"authType": "source",
				"createBy": "admin",
				"level": 0,
				"mode": 0,
				"dataSourceId": "0",
				"name": "当年销量增长趋势",
				"label": "当年销量增长趋势",
				"privileges": "use",
				"children": null,
				"allLeafs": 0
			}, {
				"id": "0f9cd623-f319-4bb5-9751-7478abee3bd2",
				"pid": "2c200620-f2f3-4224-a41d-381fa061591b",
				"nodeType": "leaf",
				"modelType": "chart",
				"modelInnerType": "line",
				"authType": "source",
				"createBy": "admin",
				"level": 0,
				"mode": 0,
				"dataSourceId": "0",
				"name": "当年销售额增长趋势",
				"label": "当年销售额增长趋势",
				"privileges": "use",
				"children": null,
				"allLeafs": 0
			}, {
				"id": "5ad64afc-132c-40ea-8f69-2f8bfe6b31d4",
				"pid": "2c200620-f2f3-4224-a41d-381fa061591b",
				"nodeType": "leaf",
				"modelType": "chart",
				"modelInnerType": "bar",
				"authType": "source",
				"createBy": "admin",
				"level": 0,
				"mode": 0,
				"dataSourceId": "0",
				"name": "各部门销售额分布",
				"label": "各部门销售额分布",
				"privileges": "use",
				"children": null,
				"allLeafs": 0
			}, {
				"id": "ebac2821-d1a0-4f26-b5d9-cd5c60ac75ab",
				"pid": "2c200620-f2f3-4224-a41d-381fa061591b",
				"nodeType": "leaf",
				"modelType": "chart",
				"modelInnerType": "line-stack",
				"authType": "source",
				"createBy": "admin",
				"level": 0,
				"mode": 0,
				"dataSourceId": "0",
				"name": "各产品销售额分布",
				"label": "各产品销售额分布",
				"privileges": "use",
				"children": null,
				"allLeafs": 0
			}, {
				"id": "2de7c3d3-e642-4509-aff1-b2520ebfe85e",
				"pid": "2c200620-f2f3-4224-a41d-381fa061591b",
				"nodeType": "leaf",
				"modelType": "chart",
				"modelInnerType": "text",
				"authType": "source",
				"createBy": "admin",
				"level": 0,
				"mode": 0,
				"dataSourceId": "0",
				"name": "销量MTD",
				"label": "销量MTD",
				"privileges": "use",
				"children": null,
				"allLeafs": 0
			}, {
				"id": "984059ca-3f9d-4ee4-9616-e409dd11991e",
				"pid": "2c200620-f2f3-4224-a41d-381fa061591b",
				"nodeType": "leaf",
				"modelType": "chart",
				"modelInnerType": "text",
				"authType": "source",
				"createBy": "admin",
				"level": 0,
				"mode": 0,
				"dataSourceId": "0",
				"name": "销量YTD",
				"label": "销量YTD",
				"privileges": "use",
				"children": null,
				"allLeafs": 0
			}, {
				"id": "02136575-effb-4a0c-b5be-9886d20259b3",
				"pid": "2c200620-f2f3-4224-a41d-381fa061591b",
				"nodeType": "leaf",
				"modelType": "chart",
				"modelInnerType": "text",
				"authType": "source",
				"createBy": "admin",
				"level": 0,
				"mode": 0,
				"dataSourceId": "0",
				"name": "销售额YTD-",
				"label": "销售额YTD-",
				"privileges": "use",
				"children": null,
				"allLeafs": 0
			}, {
				"id": "2e6f8b45-116d-46c4-a287-f3054e798556",
				"pid": "2c200620-f2f3-4224-a41d-381fa061591b",
				"nodeType": "leaf",
				"modelType": "chart",
				"modelInnerType": "text",
				"authType": "source",
				"createBy": "admin",
				"level": 0,
				"mode": 0,
				"dataSourceId": "0",
				"name": "销售额YTD-2",
				"label": "销售额YTD-2",
				"privileges": "use",
				"children": null,
				"allLeafs": 0
			}, {
				"id": "c124c0f3-3f1f-4635-bac7-f3e1f5503099",
				"pid": "2c200620-f2f3-4224-a41d-381fa061591b",
				"nodeType": "leaf",
				"modelType": "chart",
				"modelInnerType": "text",
				"authType": "source",
				"createBy": "admin",
				"level": 0,
				"mode": 0,
				"dataSourceId": "0",
				"name": "销售额YTD-3",
				"label": "销售额YTD-3",
				"privileges": "use",
				"children": null,
				"allLeafs": 0
			}, {
				"id": "504c0abd-7d31-4771-8ef9-a3494c7bb33c",
				"pid": "2c200620-f2f3-4224-a41d-381fa061591b",
				"nodeType": "leaf",
				"modelType": "chart",
				"modelInnerType": "text",
				"authType": "source",
				"createBy": "admin",
				"level": 0,
				"mode": 0,
				"dataSourceId": "0",
				"name": "销售额YTD-4",
				"label": "销售额YTD-4",
				"privileges": "use",
				"children": null,
				"allLeafs": 0
			}, {
				"id": "68fc74f2-1790-427a-ac22-49fb20edbe9a",
				"pid": "2c200620-f2f3-4224-a41d-381fa061591b",
				"nodeType": "leaf",
				"modelType": "chart",
				"modelInnerType": "line",
				"authType": "source",
				"createBy": "admin",
				"level": 0,
				"mode": 0,
				"dataSourceId": "0",
				"name": "销售额（销售二部）",
				"label": "销售额（销售二部）",
				"privileges": "use",
				"children": null,
				"allLeafs": 0
			}, {
				"id": "97400660-27a5-4502-a7cd-274190953a6c",
				"pid": "2c200620-f2f3-4224-a41d-381fa061591b",
				"nodeType": "leaf",
				"modelType": "chart",
				"modelInnerType": "line",
				"authType": "source",
				"createBy": "admin",
				"level": 0,
				"mode": 0,
				"dataSourceId": "0",
				"name": "销售额（销售三部）",
				"label": "销售额（销售三部）",
				"privileges": "use",
				"children": null,
				"allLeafs": 0
			}, {
				"id": "07ece816-f983-493e-b25d-7bfb467d787d",
				"pid": "2c200620-f2f3-4224-a41d-381fa061591b",
				"nodeType": "leaf",
				"modelType": "chart",
				"modelInnerType": "line",
				"authType": "source",
				"createBy": "admin",
				"level": 0,
				"mode": 0,
				"dataSourceId": "0",
				"name": "销售额（销售四部）",
				"label": "销售额（销售四部）",
				"privileges": "use",
				"children": null,
				"allLeafs": 0
			}, {
				"id": "a1d7bfef-f20c-4739-bfe4-cc55ed0b3fc8",
				"pid": "2c200620-f2f3-4224-a41d-381fa061591b",
				"nodeType": "leaf",
				"modelType": "chart",
				"modelInnerType": "line",
				"authType": "source",
				"createBy": "admin",
				"level": 0,
				"mode": 0,
				"dataSourceId": "0",
				"name": "销售额（销售一部）",
				"label": "销售额（销售一部）",
				"privileges": "use",
				"children": null,
				"allLeafs": 0
			}],
			"allLeafs": 0
		}, {
			"id": "bc7542d8-2b7e-4909-81ff-3627b0227501",
			"pid": "3f551269-d985-4633-884d-d118704da2db",
			"nodeType": "spine",
			"modelType": "chart",
			"modelInnerType": "group",
			"authType": "source",
			"createBy": "admin",
			"level": 0,
			"mode": 0,
			"dataSourceId": "0",
			"name": "全国GDP",
			"label": "全国GDP",
			"privileges": "use",
			"children": [{
				"id": "a0058881-b29f-4b5c-911f-7f1480b07eb0",
				"pid": "bc7542d8-2b7e-4909-81ff-3627b0227501",
				"nodeType": "leaf",
				"modelType": "chart",
				"modelInnerType": "text",
				"authType": "source",
				"createBy": "admin",
				"level": 0,
				"mode": 0,
				"dataSourceId": "0",
				"name": "2021年GDP同比增长率",
				"label": "2021年GDP同比增长率",
				"privileges": "use",
				"children": null,
				"allLeafs": 0
			}, {
				"id": "f257452d-6fc1-4499-bdce-bd10b3e1c520",
				"pid": "bc7542d8-2b7e-4909-81ff-3627b0227501",
				"nodeType": "leaf",
				"modelType": "chart",
				"modelInnerType": "text",
				"authType": "source",
				"createBy": "admin",
				"level": 0,
				"mode": 0,
				"dataSourceId": "0",
				"name": "2021年GDP总值",
				"label": "2021年GDP总值",
				"privileges": "use",
				"children": null,
				"allLeafs": 0
			}, {
				"id": "8271c4e4-43ab-48c6-b7b4-67ccaba3f80b",
				"pid": "bc7542d8-2b7e-4909-81ff-3627b0227501",
				"nodeType": "leaf",
				"modelType": "chart",
				"modelInnerType": "text",
				"authType": "source",
				"createBy": "admin",
				"level": 0,
				"mode": 0,
				"dataSourceId": "0",
				"name": "2021年GDP总值-copy(1)",
				"label": "2021年GDP总值-copy(1)",
				"privileges": "use",
				"children": null,
				"allLeafs": 0
			}, {
				"id": "c4943403-4960-4ad8-a9c5-12c46c538c34",
				"pid": "bc7542d8-2b7e-4909-81ff-3627b0227501",
				"nodeType": "leaf",
				"modelType": "chart",
				"modelInnerType": "bar-horizontal",
				"authType": "source",
				"createBy": "admin",
				"level": 0,
				"mode": 0,
				"dataSourceId": "0",
				"name": "GDP前十强城市",
				"label": "GDP前十强城市",
				"privileges": "use",
				"children": null,
				"allLeafs": 0
			}, {
				"id": "84b444e1-0088-44f9-acdc-cc39018413bc",
				"pid": "bc7542d8-2b7e-4909-81ff-3627b0227501",
				"nodeType": "leaf",
				"modelType": "chart",
				"modelInnerType": "map",
				"authType": "source",
				"createBy": "admin",
				"level": 0,
				"mode": 0,
				"dataSourceId": "0",
				"name": "今年上半年GDP（亿元）",
				"label": "今年上半年GDP（亿元）",
				"privileges": "use",
				"children": null,
				"allLeafs": 0
			}, {
				"id": "f8d62b2b-b99a-4b6c-8378-d7c2ec4ea766",
				"pid": "bc7542d8-2b7e-4909-81ff-3627b0227501",
				"nodeType": "leaf",
				"modelType": "chart",
				"modelInnerType": "pie",
				"authType": "source",
				"createBy": "admin",
				"level": 0,
				"mode": 0,
				"dataSourceId": "0",
				"name": "今年上半年GDP产业分布（亿元）",
				"label": "今年上半年GDP产业分布（亿元）",
				"privileges": "use",
				"children": null,
				"allLeafs": 0
			}, {
				"id": "c68db172-2df2-4aa2-aad6-077cf1684e14",
				"pid": "bc7542d8-2b7e-4909-81ff-3627b0227501",
				"nodeType": "leaf",
				"modelType": "chart",
				"modelInnerType": "line",
				"authType": "source",
				"createBy": "admin",
				"level": 0,
				"mode": 0,
				"dataSourceId": "0",
				"name": "历年GDP数据",
				"label": "历年GDP数据",
				"privileges": "use",
				"children": null,
				"allLeafs": 0
			}, {
				"id": "c36cd358-0501-4f83-a323-f754485d00b1",
				"pid": "bc7542d8-2b7e-4909-81ff-3627b0227501",
				"nodeType": "leaf",
				"modelType": "chart",
				"modelInnerType": "scatter",
				"authType": "source",
				"createBy": "admin",
				"level": 0,
				"mode": 0,
				"dataSourceId": "0",
				"name": "全国百强县分布",
				"label": "全国百强县分布",
				"privileges": "use",
				"children": null,
				"allLeafs": 0
			}],
			"allLeafs": 0
		}, {
			"id": "5a8e8b0a-2f64-4d1b-aac1-d284b2b8436f",
			"pid": "3f551269-d985-4633-884d-d118704da2db",
			"nodeType": "spine",
			"modelType": "chart",
			"modelInnerType": "group",
			"authType": "source",
			"createBy": "admin",
			"level": 0,
			"mode": 0,
			"dataSourceId": "0",
			"name": "新冠疫情",
			"label": "新冠疫情",
			"privileges": "use",
			"children": [{
				"id": "4de97755-5d5a-4fe0-9af0-27601f967787",
				"pid": "5a8e8b0a-2f64-4d1b-aac1-d284b2b8436f",
				"nodeType": "spine",
				"modelType": "chart",
				"modelInnerType": "group",
				"authType": "source",
				"createBy": "admin",
				"level": 0,
				"mode": 0,
				"dataSourceId": "0",
				"name": "国内疫情分析",
				"label": "国内疫情分析",
				"privileges": "use",
				"children": [{
					"id": "57760693-15db-4de9-9170-55ee7d1eb0eb",
					"pid": "4de97755-5d5a-4fe0-9af0-27601f967787",
					"nodeType": "leaf",
					"modelType": "chart",
					"modelInnerType": "bar-horizontal",
					"authType": "source",
					"createBy": "admin",
					"level": 0,
					"mode": 0,
					"dataSourceId": "0",
					"name": "病例数TOP5城市",
					"label": "病例数TOP5城市",
					"privileges": "use",
					"children": null,
					"allLeafs": 0
				}, {
					"id": "de923e69-df6b-4f61-9391-da2987f77b51",
					"pid": "4de97755-5d5a-4fe0-9af0-27601f967787",
					"nodeType": "leaf",
					"modelType": "chart",
					"modelInnerType": "text",
					"authType": "source",
					"createBy": "admin",
					"level": 0,
					"mode": 0,
					"dataSourceId": "0",
					"name": "较上日新增",
					"label": "较上日新增",
					"privileges": "use",
					"children": null,
					"allLeafs": 0
				}, {
					"id": "1aad98e5-3f99-4c0a-aa75-ca9236de0f09",
					"pid": "4de97755-5d5a-4fe0-9af0-27601f967787",
					"nodeType": "leaf",
					"modelType": "chart",
					"modelInnerType": "text",
					"authType": "source",
					"createBy": "admin",
					"level": 0,
					"mode": 0,
					"dataSourceId": "0",
					"name": "境外输入",
					"label": "境外输入",
					"privileges": "use",
					"children": null,
					"allLeafs": 0
				}, {
					"id": "ce33ad2c-3915-41cc-8d86-55405456ed05",
					"pid": "4de97755-5d5a-4fe0-9af0-27601f967787",
					"nodeType": "leaf",
					"modelType": "chart",
					"modelInnerType": "text",
					"authType": "source",
					"createBy": "admin",
					"level": 0,
					"mode": 0,
					"dataSourceId": "0",
					"name": "累计接种",
					"label": "累计接种",
					"privileges": "use",
					"children": null,
					"allLeafs": 0
				}, {
					"id": "03410ec1-1bd0-4afd-ac37-9306e00e328c",
					"pid": "4de97755-5d5a-4fe0-9af0-27601f967787",
					"nodeType": "leaf",
					"modelType": "chart",
					"modelInnerType": "text",
					"authType": "source",
					"createBy": "admin",
					"level": 0,
					"mode": 0,
					"dataSourceId": "0",
					"name": "累计死亡",
					"label": "累计死亡",
					"privileges": "use",
					"children": null,
					"allLeafs": 0
				}, {
					"id": "9ecb6827-f47f-4b19-b788-81a6b55940af",
					"pid": "4de97755-5d5a-4fe0-9af0-27601f967787",
					"nodeType": "leaf",
					"modelType": "chart",
					"modelInnerType": "text",
					"authType": "source",
					"createBy": "admin",
					"level": 0,
					"mode": 0,
					"dataSourceId": "0",
					"name": "累计治愈",
					"label": "累计治愈",
					"privileges": "use",
					"children": null,
					"allLeafs": 0
				}, {
					"id": "93a58625-3730-4a07-99bd-75f174ff428d",
					"pid": "4de97755-5d5a-4fe0-9af0-27601f967787",
					"nodeType": "leaf",
					"modelType": "chart",
					"modelInnerType": "line",
					"authType": "source",
					"createBy": "admin",
					"level": 0,
					"mode": 0,
					"dataSourceId": "0",
					"name": "全国现有确诊趋势",
					"label": "全国现有确诊趋势",
					"privileges": "use",
					"children": null,
					"allLeafs": 0
				}, {
					"id": "2f9bf4d5-b1d3-4cac-9df2-2c8827d65bbf",
					"pid": "4de97755-5d5a-4fe0-9af0-27601f967787",
					"nodeType": "leaf",
					"modelType": "chart",
					"modelInnerType": "line",
					"authType": "source",
					"createBy": "admin",
					"level": 0,
					"mode": 0,
					"dataSourceId": "0",
					"name": "全国新增确诊趋势",
					"label": "全国新增确诊趋势",
					"privileges": "use",
					"children": null,
					"allLeafs": 0
				}, {
					"id": "3a5e4081-4cd5-427f-bd3a-ff7815efaf25",
					"pid": "4de97755-5d5a-4fe0-9af0-27601f967787",
					"nodeType": "leaf",
					"modelType": "chart",
					"modelInnerType": "text",
					"authType": "source",
					"createBy": "admin",
					"level": 0,
					"mode": 0,
					"dataSourceId": "0",
					"name": "无症状感染者",
					"label": "无症状感染者",
					"privileges": "use",
					"children": null,
					"allLeafs": 0
				}, {
					"id": "0d8bc9d7-b76b-4ec5-96e7-0df1c3426205",
					"pid": "4de97755-5d5a-4fe0-9af0-27601f967787",
					"nodeType": "leaf",
					"modelType": "chart",
					"modelInnerType": "text",
					"authType": "source",
					"createBy": "admin",
					"level": 0,
					"mode": 0,
					"dataSourceId": "0",
					"name": "现有确诊",
					"label": "现有确诊",
					"privileges": "use",
					"children": null,
					"allLeafs": 0
				}, {
					"id": "eaa8947b-d9e7-4ca4-ba65-08965dfa620c",
					"pid": "4de97755-5d5a-4fe0-9af0-27601f967787",
					"nodeType": "leaf",
					"modelType": "chart",
					"modelInnerType": "text",
					"authType": "source",
					"createBy": "admin",
					"level": 0,
					"mode": 0,
					"dataSourceId": "0",
					"name": "新增确诊",
					"label": "新增确诊",
					"privileges": "use",
					"children": null,
					"allLeafs": 0
				}, {
					"id": "5f694f25-b0fd-45f6-acbd-9dd338e196ce",
					"pid": "4de97755-5d5a-4fe0-9af0-27601f967787",
					"nodeType": "leaf",
					"modelType": "chart",
					"modelInnerType": "gauge",
					"authType": "source",
					"createBy": "admin",
					"level": 0,
					"mode": 0,
					"dataSourceId": "0",
					"name": "疫苗接种",
					"label": "疫苗接种",
					"privileges": "use",
					"children": null,
					"allLeafs": 0
				}],
				"allLeafs": 0
			}],
			"allLeafs": 0
		}],
		"allLeafs": 0
	}
]
})
const getDatas = Mock.mock({
	"data": {
		"table": {
			"id": "077e3af4-a544-43bc-ab48-a3a161b51ba8",
			"name": "demo_demo_olympiad_gold_date",
			"sceneId": "dfe82631-0ca9-4bd9-a6bd-9a2531d37bf3",
			"dataSourceId": "76026997-94f9-4a35-96ca-151084638969",
			"type": "db",
			"mode": 0,
			"createBy": "admin",
			"createTime": 1628136405549,
			"qrtzInstance": "3420075446f916280724849991628072487409",
			"syncStatus": "Completed",
			"lastUpdateTime": 1628136420093,
			"info": "{\"table\":\"demo_olympiad_gold_date\"}"
		},
		"datasource": {
			"id": "76026997-94f9-4a35-96ca-151084638969",
			"name": "demo",
			"desc": "demo",
			"type": "mysql",
			"createTime": 1624247414781,
			"updateTime": 1624247414781,
			"createBy": "admin",
			"status": "Success",
			"configuration": null
		},
		"chart": {
			"id": "1d06e2a0-d936-4192-b523-2eb1e8cebd51",
			"name": "夺金项目排行榜",
			"sceneId": "bfa7d87f-c76f-4406-9f19-0adccb7c568d",
			"tableId": "077e3af4-a544-43bc-ab48-a3a161b51ba8",
			"type": "bar",
			"render": "echarts",
			"resultCount": 1000,
			"resultMode": "custom",
			"title": "夺金项目排行榜",
			"createBy": "admin",
			"createTime": 1628136691089,
			"updateTime": 1645426370684,
			"stylePriority": "view",
			"extStack": "[]",
			"extBubble": "[]",
			"customAttr": "{\"color\":{\"value\":\"future\",\"colors\":[\"#63b2ee\",\"#76da91\",\"#f8cb7f\",\"#f89588\",\"#7cd6cf\",\"#9192ab\",\"#7898e1\",\"#efa666\",\"#eddd86\"],\"alpha\":80,\"tableHeaderBgColor\":\"#4e81bb\",\"tableItemBgColor\":\"#c6d9f0\",\"tableFontColor\":\"#000000\",\"tableStripe\":true,\"dimensionColor\":\"#000000\",\"quotaColor\":\"#000000\"},\"tableColor\":{\"value\":\"default\",\"colors\":[\"#5470c6\",\"#91cc75\",\"#fac858\",\"#ee6666\",\"#73c0de\",\"#3ba272\",\"#fc8452\",\"#9a60b4\",\"#ea7ccc\"],\"alpha\":100,\"tableHeaderBgColor\":\"#4e81bb\",\"tableItemBgColor\":\"#c6d9f0\",\"tableFontColor\":\"#000000\",\"tableStripe\":true,\"dimensionColor\":\"#000000\",\"quotaColor\":\"#000000\"},\"size\":{\"barDefault\":true,\"barWidth\":40,\"barGap\":0.4,\"lineWidth\":1,\"lineType\":\"solid\",\"lineSymbol\":\"emptyCircle\",\"lineSymbolSize\":4,\"lineSmooth\":false,\"lineArea\":false,\"pieInnerRadius\":0,\"pieOuterRadius\":80,\"pieRoseType\":\"radius\",\"pieRoseRadius\":5,\"funnelWidth\":80,\"radarShape\":\"circle\",\"tableTitleFontSize\":12,\"tableItemFontSize\":12,\"tableTitleHeight\":36,\"tableItemHeight\":36,\"gaugeMin\":0,\"gaugeMax\":100,\"gaugeStartAngle\":225,\"gaugeEndAngle\":-45,\"dimensionFontSize\":18,\"quotaFontSize\":18,\"spaceSplit\":10,\"dimensionShow\":true,\"quotaShow\":true},\"label\":{\"show\":true,\"position\":\"top\",\"color\":\"#FFFFFF\",\"fontSize\":\"10\",\"formatter\":\"{c}\",\"gaugeFormatter\":\"{value}\",\"labelLine\":{\"show\":true}},\"tooltip\":{\"show\":true,\"trigger\":\"item\",\"confine\":true,\"textStyle\":{\"fontSize\":\"10\",\"color\":\"#909399\"},\"formatter\":\"\"}}",
			"customStyle": "{\"text\":{\"show\":true,\"fontSize\":\"16\",\"color\":\"#FFFFFF\",\"hPosition\":\"center\",\"vPosition\":\"top\",\"isItalic\":false,\"isBolder\":false,\"title\":\"夺金项目排行榜\"},\"legend\":{\"show\":true,\"hPosition\":\"center\",\"vPosition\":\"bottom\",\"orient\":\"horizontal\",\"icon\":\"rect\",\"textStyle\":{\"color\":\"#FFFFFF\",\"fontSize\":\"10\"}},\"xAxis\":{\"show\":true,\"position\":\"bottom\",\"name\":\"\",\"nameTextStyle\":{\"color\":\"#333333\",\"fontSize\":12},\"axisLabel\":{\"show\":true,\"color\":\"#FFFFFF\",\"fontSize\":\"10\",\"rotate\":42,\"formatter\":\"{value}\"},\"splitLine\":{\"show\":false,\"lineStyle\":{\"color\":\"#cccccc\",\"width\":1,\"style\":\"solid\"}}},\"yAxis\":{\"show\":true,\"position\":\"left\",\"name\":\"\",\"nameTextStyle\":{\"color\":\"#333333\",\"fontSize\":12},\"axisLabel\":{\"show\":true,\"color\":\"#333333\",\"fontSize\":\"12\",\"rotate\":0,\"formatter\":\"{value}\"},\"splitLine\":{\"show\":false,\"lineStyle\":{\"color\":\"#cccccc\",\"width\":1,\"style\":\"solid\"}}},\"background\":{\"color\":\"#ffffff\",\"alpha\":10},\"split\":{\"name\":{\"show\":true,\"color\":\"#999999\",\"fontSize\":\"12\"},\"splitNumber\":5,\"axisLine\":{\"show\":true,\"lineStyle\":{\"color\":\"#FFFFFF\",\"width\":1,\"type\":\"solid\"}},\"axisTick\":{\"show\":false,\"length\":5,\"lineStyle\":{\"color\":\"#999999\",\"width\":1,\"type\":\"solid\"}},\"axisLabel\":{\"show\":false,\"rotate\":0,\"margin\":8,\"color\":\"#FFFFFF\",\"fontSize\":\"12\",\"formatter\":\"{value}\"},\"splitLine\":{\"show\":true,\"lineStyle\":{\"color\":\"#FFFFFF\",\"width\":1,\"type\":\"solid\"}},\"splitArea\":{\"show\":true}}}",
			"customFilter": "[]",
			"drillFields": "[]",
			"snapshot": null,
			"xaxis": "[{\"id\":\"a3929ad0-0f0d-41f3-a3dd-c5b4ce8b4bfd\",\"tableId\":\"077e3af4-a544-43bc-ab48-a3a161b51ba8\",\"originName\":\"game\",\"name\":\"game\",\"dataeaseName\":\"C_c8d46d341bea4fd5bff866a65ff8aea9\",\"groupType\":\"d\",\"type\":\"VARCHAR\",\"size\":255,\"deType\":0,\"deTypeFormat\":null,\"deExtractType\":0,\"extField\":0,\"checked\":true,\"columnIndex\":2,\"lastSyncTime\":1628136405582,\"dateStyle\":\"y_M_d\",\"datePattern\":\"date_sub\",\"sort\":\"none\",\"filter\":[]}]",
			"yaxis": "[{\"id\":\"07ab49dd-a0f5-4f12-9d76-54e88b257bb8\",\"tableId\":\"077e3af4-a544-43bc-ab48-a3a161b51ba8\",\"originName\":\"qty\",\"name\":\"金牌数\",\"dataeaseName\":\"C_f44fc6cd274569fd327d9bccc761b066\",\"groupType\":\"q\",\"type\":\"VARCHAR\",\"size\":255,\"deType\":2,\"deTypeFormat\":null,\"deExtractType\":0,\"extField\":0,\"checked\":true,\"columnIndex\":3,\"lastSyncTime\":1628136405582,\"summary\":\"sum\",\"sort\":\"desc\",\"filter\":[],\"index\":0,\"renameType\":\"quota\",\"compareCalc\":{\"type\":\"none\",\"resultData\":\"percent\",\"field\":\"\",\"custom\":{\"field\":\"\",\"calcType\":\"0\",\"timeType\":\"0\",\"currentTime\":\"\",\"compareTime\":\"\",\"currentTimeRange\":[],\"compareTimeRange\":[]}},\"chartType\":\"bar\"}]",
			"yaxisExt": "[]"
		}
	}
})
module.exports = [
  // {
  //   url: '/authModel/queryAuthModel',
  //   type: 'post',
  //   response: config => {
  //     const items = datas.items
  //     return {
  //       code: 20000,
  //       data: items
  //     }
  //   }
  // },
  // {
  //   url: '/chart/view/getData/',
  //   type: 'post',
  //   response: config => {
  //     const items = getDatas.data
  //     return {
  //       code: 20000,
  //       data: items
  //     }
  //   }
  // }

]
