const Mock = require('mockjs')

const engineList = Mock.mock({
  // 'items|30': [{
  //   id: '@id',
  //   title: '@sentence(7, 8)',
  //   name: '@name',
  //   'sex|1': ['男', '女'],
  //   'age|20-30': 20
  // }]
})

module.exports = [
  {
    url: '/engine',
    type: 'get',
    response: config => {
      const items = engineList.items
      return {
        code: 20000,
        data: {
          total: items.length,
          items: items
        }
      }
    }
  }
]
