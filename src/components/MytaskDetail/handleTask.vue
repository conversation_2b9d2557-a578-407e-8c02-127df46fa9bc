<template>
  <el-dialog :visible="visible" v-bind="$attrs" width="600px" :close-on-click-modal="false" v-on="$listeners" @close="_close">
    <template slot="title">
      <template v-if="type === 1">办理</template>
      <template v-else-if="type === 2">{{ btnName }}</template>
    </template>
    <div v-loading="!approvalData">
      <el-form v-if="approvalData" ref="dialogForm" :model="ruleForm" :rules="rules">
        <div v-if="type === 1 || type === 2">
          <el-form-item v-if="!nextNode.conditionResult" label="下一节点">
            {{ nextNode.name }}
            <span v-if="approvalData.isCountersign == 'true'" class="el-badge__content ml-3">会签</span>
            <span v-if="approvalData.isCallActivity == 'true'" class="el-badge__content ml-3">动态子流程</span>
          </el-form-item>
          <el-form-item v-else label="下一节点" prop="chooseNode">
            <!-- <el-checkbox-group v-model="ruleForm.chooseNode">
                <el-checkbox v-for="(item, index) in approvalData.nodeDetails" :key="index" :label="item.nodeId">{{ item.nodeName }}</el-checkbox>
            </el-checkbox-group> -->
            <el-radio-group v-model="ruleForm.chooseNode" @change="nodeChange">
              <el-radio v-for="(item, index) in approvalData" :key="index" :label="item.id">{{ item.name }}</el-radio>
            </el-radio-group>
          </el-form-item>
          <el-form-item v-if="!isEnd" label="审批人" prop="chooseNodeUser">
            <el-checkbox-group v-model="ruleForm.chooseNodeUser">
              <el-checkbox v-for="(item, index) in userList" :key="index" :label="item.id">{{ item.name }}</el-checkbox>
            </el-checkbox-group>
          </el-form-item>
        </div>
        <el-form-item v-if="approvalData.isCallActivity == 'false'" label="任务标题" prop="taskTitle">
          <el-input v-model="ruleForm.taskTitle" />
        </el-form-item>
        <!-- <el-form-item label="优先级" prop="priority">
          <vone-icon-select
            v-model="ruleForm.priority"
            :data="prioritList"
            placeholder="请选择优先级"
            filterable
            clearable
          >
            <el-option
              v-for="item in prioritList"
              :key="item.code"
              :label="item.name"
              :value="item.code"
            >
              <div style="display: flex; align-items: center">
                <i
                  :class="`iconfont ${item.icon}`"
                  :style="{ color: item.color, fontSize: '16px', paddingRight: '6px' }"
                />
                <span>{{ item.name }}</span>
              </div>
            </el-option>
          </vone-icon-select>
        </el-form-item> -->
        <el-form-item label="审批意见" prop="message">
          <el-input v-model="ruleForm.message" :rows="2" type="textarea" />
        </el-form-item>
      </el-form>
    </div>
    <div slot="footer" class="dialog-footer">
      <el-button @click="_close">取消</el-button>
      <el-button type="primary" :loading="saveLoading" @click="submit">确定</el-button>
    </div>
  </el-dialog>
</template>

<script>
import storage from 'store'
// import _ from 'lodash'
// import { getNextNodeInfo, passProcessTask } from '@/api/vone/workflow/task' // 已删除
// import { editReleasePlanFlowStatus, editWorkOrderFlowStatus } from '@/api/vone/release/mytask' // 已删除
export default {
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    data: {
      type: Object,
      default: () => ({})
    },
    type: {
      type: Number,
      default: 1
    },
    btnName: {
      type: String,
      default: ''
    },
    codeId: {
      type: String,
      default: ''
    }
  },
  data() {
    const { taskId, processId } = this.$route.query
    return {
      taskId,
      processId,
      saveLoading: false,
      approvalData: [],
      ruleForm: {
        message: '',
        taskTitle: '',
        priority: 3,
        chooseNode: '',
        chooseNodeUser: [],
        subProcessDatas: [],
        conditionResult: undefined
      },
      userList: [],
      rules: {
        message: [{ required: true, message: '请输入审批意见' }, { pattern: /^[^\s]*$/, message: '审批意见不能输入空格' }],
        taskTitle: [{ required: true, message: '请输入任务标题' }, { pattern: /^[^\s]*$/, message: '任务标题不能输入空格' }],
        priority: [{ required: true, message: '请选择优先级' }],
        chooseNodeUser: [{ required: true, type: 'array', message: '请选择审批人' }],
        chooseNode: [{ required: true, message: '请选择下一节点' }],

        // 动态子流程
        process: [{ required: true, message: '请选择流程' }],
        user: [{ required: true, type: 'array', message: '请选择审批人' }]
      },
      prioritList: [{
        name: '最高',
        code: 5,
        icon: 'el-icon-icon-dengji-zuigao2',
        color: '#FA6A69'
      }, {
        name: '较高',
        code: 4,
        icon: 'el-icon-icon-dengji-jiaogao2',
        color: '#FA8669'
      }, {
        name: '普通',
        code: 3,
        icon: 'el-icon-icon-dengji-putong2',
        color: 'var(--main-theme-color,#3e7bfa)'
      }, {
        name: '较低',
        code: 2,
        icon: 'el-icon-icon-dengji-jiaodi2',
        color: '#5ACC5E'
      }, {
        name: '最低',
        code: 1,
        icon: 'el-icon-icon-dengji-zuidi2',
        color: '#4ECF95'
      }
      ]
    }
  },
  computed: {
    loginUser() {
      return storage.get('user') || {}
    },
    // 下一节点数据
    nextNode() {
      return this.approvalData?.[0] || {}
    },
    isEnd() {
      if (this.approvalData.length) {
        return /^EndEvent/.test(this.approvalData?.[0].type) || /^endEvent/.test(this.approvalData?.[0].type)
      }
      return false
    }
  },
  watch: {
    visible: {
      handler(v) {
        if (v) {
          this.getFlowNextTask()
        }
      },
      immediate: true
    }
  },
  methods: {
    /**
     * 查询审批节点
     */
    async getFlowNextTask() {
      const params = {
        'variables': {},
        taskId: this.taskId,
        processInstanceId: this.processId
      }
      const { data, isSuccess, msg } = await getNextNodeInfo(params)
      if (!isSuccess) {
        this.$message.warning(msg)
        return
      }
      this.approvalData = data
      // 设置下一节点默认值
      this.ruleForm.chooseNode = data?.[0]?.id
      this.nodeChange(this.ruleForm.chooseNode)
      // 设置子流程
      this.initProcessItem()
    },
    initProcessItem() {
      if (this.isCallActivity() && this.ruleForm.subProcessDatas.length === 0) {
        this.ruleForm.subProcessDatas = []
        this.addProcessItem()
      }
    },
    isCallActivity() {
      return this.approvalData && this.approvalData.length && this.approvalData.isCallActivity == 'true'
    },
    async submit() {
      try {
        await this.$refs.dialogForm.validate()
      } catch (error) {
        this.$message.warning('请检查表单信息')
        return
      }
      // 用户集合，会签时使用
      // const userList = []
      // this.ruleForm.chooseNodeUser.map(m => {
      //   this.userList.map(item => {
      //     if (m == item.id) {
      //       userList.push({ id: item.id, name: item.name })
      //     }
      //   })
      // })
      const params = {
        'comment': this.ruleForm.message,
        'completeAssignee': this.loginUser.id,
        'taskId': this.taskId
      }
      if (this.nextNode.type != 'endEvent' || this.nextNode.type != 'EndEvent') {
        params['variables'] = {
          assignee: this.ruleForm.chooseNodeUser.join(',')
        }
      }

      passProcessTask(params).then(async res => {
        if (!res.isSuccess) {
          this.$message.warning(res.msg)
          return
        }
        if (this.$route.query.release_type == 'RP') {
          await editReleasePlanFlowStatus({
            id: this.$route.query.releaseId,
            status: this.nextNode?.name
          }).then(res => {}).catch(() => {})
        }
        if (this.$route.query.release_type == 'RWP') {
          await editWorkOrderFlowStatus({
            id: this.$route.query.releaseId,
            processStatus: this.nextNode?.name
          }).then(res => {}).catch(() => {})
        }
        this.$message.success(res.msg)
        this.$emit('update:visible', false)
        await history.back()
      })

      // let nodeDetails = []
      // if (this.nextNode.conditionResult) {
      //   this.approvalData.forEach(item => {
      //     if (item.id == this.ruleForm.chooseNode) {
      //       item.userDatas = []
      //       this.ruleForm.conditionResult = item.conditionResult

      //       item.userDatas = userList
      //       nodeDetails.push({
      //         userDatas: userList,
      //         nodeId: item.id,
      //         nodeName: item.name
      //       })
      //     }
      //   })
      // } else {
      //   this.approvalData.forEach(item => {
      //     item.userDatas = []
      //     delete item.defId
      //     delete item.candidate
      //     delete item.conditionResult
      //     item.userDatas = userList
      //     nodeDetails.push(item)
      //   })
      // }
      // // 未选择节点
      // if (!this.ruleForm.chooseNode) {
      //   nodeDetails = this.approvalData.nodeDetails[0]
      //   nodeDetails.userDatas = nodeDetails.candidate
      //   delete nodeDetails.candidate
      //   delete nodeDetails.conditionResult
      // }
      // const params = {
      //   taskId: this.taskId,
      //   processInstanceId: this.processId,
      //   userCode: this.loginUser.id,
      //   userName: this.loginUser.name,
      //   ..._.omit(this.ruleForm, ['chooseNodeUser', 'subProcessDatas', 'conditionResult', 'chooseNode']),
      //   type: 'SP',
      //   formData: this.data || {}
      // }
      // if (this.type == 2) {
      //   Object.assign(params, {
      //     action: 'agree'
      //   })
      // }
      // if (this.type == 3) {
      //   Object.assign(params, {
      //     action: 'unAgree'
      //   })
      // }
      // if (this.type === 1 || this.type == 2 || this.type == 3) {
      //   Object.assign(params, {
      //     targetNodeInfos: nodeDetails,
      //     variables: this.ruleForm.conditionResult
      //   })
      // }
      // this.saveLoading = true
      // const { isSuccess, msg } = await passProcessTask(params)
      // this.saveLoading = false
      // if (!isSuccess) {
      //   this.$message.warning(msg)
      //   return
      // }
      // this.$message.success(msg)
      // this.$emit('update:visible', false)
      // history.back()
    },
    addProcessItem() {
      this.ruleForm.subProcessDatas.push({
        id: Date.now(),
        users: [],
        nodes: [],
        selectedUser: [],
        taskTitle: ''
      })
    },
    _close() {
      this.$emit('update:visible', false)
    },
    nodeChange(nodeId) {
      this.approvalData.forEach(item => {
        if (item.id == nodeId) {
          this.userList = item.userList || []
        }
      })
    }
  }
}
</script>
<style lang="scss" scoped>
::v-deep {
  .el-card {
    position: relative;
    .el-button--danger {
      position: absolute;
      top: 10px;
      left: 10px;
    }
  }
}
</style>
