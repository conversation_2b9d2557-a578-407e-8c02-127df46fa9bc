<template>
  <el-dialog v-dialogCenter title="退回" v-bind="$attrs" width="600px" :close-on-click-modal="false" v-on="$listeners" @close="_close">
    <el-form ref="dialogForm" :model="ruleForm" :rules="rules">
      <!-- <el-form-item label="退回节点" prop="distFlowElementId">
        <el-select v-model="ruleForm.distFlowElementId" @change="changeBackNode">
          <el-option v-for="item in backNodes" :key="item.nodeId" :label="item.nodeName" :value="item.nodeId" />
        </el-select>
      </el-form-item>
      <el-form-item label="处理人">
        <el-input v-model="ruleForm.userName" disabled />
      </el-form-item>
      <el-form-item label="任务标题" prop="taskTitle">
        <el-input v-model="ruleForm.taskTitle" />
      </el-form-item>
      <el-form-item label="优先级" prop="priority">
        <vone-icon-select
          v-model="ruleForm.priority"
          :data="prioritList"
          placeholder="请选择优先级"
          filterable
          clearable
        >
          <el-option
            v-for="item in prioritList"
            :key="item.code"
            :label="item.name"
            :value="item.code"
          >
            <div style="display: flex; align-items: center">
              <i
                :class="`iconfont ${item.icon}`"
                :style="{ color: item.color, fontSize: '16px', paddingRight: '6px' }"
              />
              <span>{{ item.name }}</span>
            </div>
          </el-option>
        </vone-icon-select>
      </el-form-item> -->
      <el-form-item label="审批意见" prop="message">
        <el-input v-model="ruleForm.message" :rows="3" type="textarea" />
      </el-form-item>
    </el-form>
    <div slot="footer" class="dialog-footer">
      <el-button @click="_close">取消</el-button>
      <el-button :loading="saveLoading" type="primary" @click="submit">确定</el-button>
    </div>
  </el-dialog>
</template>

<script>
import storage from 'store'
// import { flowOperationHandle, getFlowBackNodeList } from '@/api/vone/flow/task'
// import { rejectProcessTask } from '@/api/vone/workflow/task' // 已删除
export default {
  directives: {
    dialogCenter(el) {
      const containerH = document.body.clientHeight
      const dialog = el.childNodes[0]
      const dialogH = dialog.clientHeight
      const child = dialog.querySelector('.el-dialog__body')

      if (!child || dialogH === 0) return
      const height = (containerH - dialogH) / 2

      height > 0 ? dialog.style.margin = `${height}px auto` : dialog.style.margin = '5vh auto'
    }
  },
  props: {
    approvalTitle: {
      type: String,
      default: ''
    }
  },
  data() {
    const { taskId, processId } = this.$route.query
    return {
      taskId,
      processId,
      saveLoading: false,
      backNodes: [],
      ruleForm: {
        taskTitle: ''
      },
      rules: {
        distFlowElementId: [{ required: true, message: '请选择退回节点' }],
        message: [{ required: true, message: '请输入审批意见' }, { pattern: /^[^\s]*$/, message: '审批意见不能输入空格' }],
        taskTitle: [{ required: true, message: '请输入任务标题' }, { pattern: /^[^\s]*$/, message: '任务标题不能输入空格' }],
        priority: [{ required: true, message: '请选择优先级' }]
      },
      prioritList: [{
        name: '最高',
        code: 5,
        icon: 'el-icon-icon-dengji-zuigao2',
        color: '#FA6A69'
      }, {
        name: '较高',
        code: 4,
        icon: 'el-icon-icon-dengji-jiaogao2',
        color: '#FA8669'
      }, {
        name: '普通',
        code: 3,
        icon: 'el-icon-icon-dengji-putong2',
        color: 'var(--main-theme-color,#3e7bfa)'
      }, {
        name: '较低',
        code: 2,
        icon: 'el-icon-icon-dengji-jiaodi2',
        color: '#5ACC5E'
      }, {
        name: '最低',
        code: 1,
        icon: 'el-icon-icon-dengji-zuidi2',
        color: '#4ECF95'
      }
      ]
    }
  },
  computed: {
    loginUser() {
      return storage.get('user') || {}
    }
  },
  watch: {
    '$attrs.visible'(v) {
      if (v) {
        // this.getTaskRejectBackNode()
        this.ruleForm = {
          taskTitle: this.approvalTitle,
          priority: 4
        }
      }
    }
  },
  methods: {
    /**
     * 查询退回节点数据
     */
    // async getTaskRejectBackNode() {
    //   const { data, isSuccess, msg } = await getFlowBackNodeList(this.processId, this.taskId)
    //   if (!isSuccess) {
    //     this.$message.warning(msg)
    //     return
    //   }
    //   this.backNodes = data
    // },
    async submit() {
      try {
        await this.$refs.dialogForm.validate()
      } catch (error) {
        this.$message.warning('请检查表单信息')
        return
      }
      const params = {
        'comment': this.ruleForm.message,
        'rejectAssignee': this.loginUser.id,
        'taskId': this.taskId,
        'variables': {
        }
      }

      rejectProcessTask(params).then(res => {
        if (res.isSuccess) {
          this.$message.success(res.msg)
          this.$emit('update:visible', false)
          history.back()
        } else {
          this.$message.warning(res.msg)
        }
      })
      // this.saveLoading = true
      // const formData = {
      //   userCode: this.loginUser.id,
      //   userName: this.loginUser.name,
      //   taskId: this.taskId,
      //   type: 'BH',
      //   processInstanceId: this.processId,
      //   ...this.ruleForm
      // }
      // const { isSuccess, msg } = await flowOperationHandle(formData, this.taskId)
      // this.saveLoading = false
      // if (isSuccess) {
      //   this.$message.success(msg)
      //   this.$emit('update:visible', false)
      //   history.back()
      // } else {
      //   this.$message.warning(msg)
      // }
    },
    // 下拉框选择
    async changeBackNode(val) {
      this.backNodes.forEach(item => {
        if (item.nodeId == val) {
          this.ruleForm = Object.assign({}, this.ruleForm, {
            backUserList: item.userCode.split(','),
            userCode: item.userCode,
            userName: item.userName,
            formData: item.hisTaskFormData
          })
        }
      })
    },
    _close() {
      this.$refs.dialogForm.resetFields()
      this.$emit('update:visible', false)
    }
  }
}
</script>
