<template>
  <div>
    <div class="border">
      <strong>基本信息</strong>
    </div>
    <!--基本信息-->
    <template>
      <slot name="detail" />
    </template>
    <div class="line" style="margin:16px 0px" />
    <div class="border">
      <strong>执行历史信息</strong>
    </div>
    <div class="stepBox">
      <!--横向步骤条-->
      <div class="vone-widthwise-steps">
        <div :style="{width: stepList.length * 190 +'px'}">
          <div v-for="(item, index) in stepList" :key="index" :style="{'z-index': stepList.length - index}" :class="['step-content',index < currentNode ?'success' : index == currentNode ? 'working' : '']">
            <span>{{ index +1 }}</span>
            {{ item.nodeName }}
          </div>
        </div>
      </div>
      <div style="display: flex;margin:16px 0px;">
        <!--流转历史记录-->
        <div style="width: 40%;">
          <div class="vone-heightwise-steps">
            <div v-for="(item, index) in historyList" :key="index">
              <div class="step-node">
                {{ item.nodeName }}
              </div>
              <div v-if="item.nodeType !== 'noneEndEvent'" class="step-content" />
              <div v-if="item.nodeType !== 'noneEndEvent'" class="step-child-node">
                <!-- <span :class="['tagbox',item.type == 'SP' ? 'success': item.type == 'BH' ? 'fail' : 'working']">{{ item.type == 'SP' ? '同意': item.type == 'BH' ? '驳回' : '进行中' }}</span> -->
                <span :class="['tagbox',item.endTime ? 'success': 'working']">
                  {{ item.endTime ? '通过': '进行中' }}
                </span>
              </div>
              <!--申请状态内容-->
              <div v-if="item.nodeType !== 'noneEndEvent'" class="step-content">
                <div class="user">
                  <vone-user-avatar v-for="(itm,index) in item.echoMap.userId" :key="index" :avatar-path="itm.avatarPath" :name="itm.name" width="20px" height="20px" />
                </div>
                <div v-if="item.endTime" class="time">{{ dayjs(item.endTime).format('YYYY-MM-DD HH:mm:ss') }}</div>
                <p class="msg">{{ item.message }}</p>
              </div>
            </div>
          </div>
        </div>
        <!--操作按钮-->
        <div v-if="!$route.query.finish" class="flowBox">
          <el-button @click="next">办理</el-button>
          <!-- <el-button @click="goBack">退回</el-button> -->
        </div>
      </div>
    </div>
    <!--办理-->
    <handleTask :visible.sync="handleTaskVisible" :type="handleTaskParams.type" :btn-name="handleTaskParams.btn && handleTaskParams.btn.btnName" :proc-id="procId" :code-id="codeData.id || null" />
    <!-- 退回 -->
    <sendBack :visible.sync="sendBackVisible" :proc-id="procId" />
  </div>
</template>
<script>
// import { getRecord, getNodeList } from '@/api/vone/workflow' // 已删除
import handleTask from './handleTask.vue'
import sendBack from './sendBack.vue'
export default {
  components: {
    handleTask,
    sendBack
  },
  props: {
    procId: { // 流程id
      type: String,
      default: ''
    },
    codeData: { // 代码库详细信息
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      stepList: [],
      currentNode: 0,
      historyList: [],
      handleTaskVisible: false,
      sendBackVisible: false,
      handleTaskParams: {
        type: 1,
        btn: ''
      }
    }
  },
  mounted() {
    this.getList()
    this.getHistoryList()
  },
  methods: {
    // 步骤条数据
    getList() {
      getNodeList(this.procId).then(res => {
        if (res.isSuccess) {
          if (res.data.activityNode) {
            res.data.nodeList.map((item, index) => {
              if (item.nodeId == res.data.activityNode) {
                this.currentNode = index
              }
            })
          } else {
            this.currentNode = res.data.nodeList.length
          }
          this.stepList = res.data.nodeList
        } else {
          this.$message.warning(res.msg)
        }
      })
    },
    // 历史流转记录
    getHistoryList() {
      getRecord(this.procId).then(res => {
        if (res.isSuccess) {
          this.historyList = res.data
        } else {
          this.$message.warning(res.msg)
        }
      })
    },
    next() {
      this.handleTaskVisible = true
    },
    goBack() {
      this.sendBackVisible = true
    }
  }
}
</script>
<style lang='scss' scoped>
.border {
  border-left: 4px solid var(--main-theme-color);
  padding-left: 10px;
  height: 24px;
  line-height: 24px;
  font-size: 16px;
  color: #1D2129;
	margin: 10px 0px;
}
.line {
  height: 1px;
  background-color: #F2F3F5;
  margin: 16px;
}
.stepBox {
  padding: 16px 40px;
}
// 横向流程图
.vone-widthwise-steps {
  width: 100%;
  overflow-x: auto;
	.step-content {
    float: left;
		width: 180px;
		height: 48px;
		line-height: 48px;
		background-color: #F7F8FA;
		position: relative;
		z-index: 1;
		margin-left: 10px;
		padding-left: 40px;
		color: #6B7385;
		font-weight: 500;
		span {
			width: 24px;
			height: 24px;
			line-height: 20px;
			display: inline-block;
			border-radius: 50%;
			text-align: center;
			border: 2px solid;
			border-color: #6B7385;
			color: #6B7385;
		}
	}
	.step-content:first-child {
		padding-left: 18px;
    margin-left: 0px;
	}
	.step-content:not(:first-child):before {
		content: "";
		border-bottom: 24px solid transparent;
    border-left: 36px solid #fff;
    border-top: 24px solid transparent;
		position: absolute;
		left: 0px;
		z-index: 0;
	}
	.step-content:not(:last-child):after {
		content: "";
		border-bottom: 24px inset transparent;
    border-left: 36px solid;
		border-left-color: #F7F8FA;
    border-top: 24px inset transparent;
		position: absolute;
		right: -36px;
		z-index: 2;
	}
	// 成功
	.success {
		background-color: #D7FADA;
		color: #2CC750;
		span {
			border-color: #2CC750;
			color: #2CC750;
		}
	}
	.success:not(:last-child):after {
		border-left-color: #D7FADA;
	}
	// 进行中
	.working {
		background-color: #D1E6FF;
		color: #2D6EF7;
		span {
			border-color: #2D6EF7;
			color: #2D6EF7;
		}
	}
	.working:not(:last-child):after {
		border-left-color: #D1E6FF;
	}
}
// 纵向流程图
.vone-heightwise-steps {
  .tagbox {
    padding: 4px 12px;
    border-radius: 20px;
    font-weight: 500;
  }
  .success {
    background-color: #D7FADA;
		color: #2CC750;
  }
  .working {
		background-color: #F0F7FF;
		color: #2D6EF7;
  }
  .fail {
    background-color: #fef0f0;
    color: #f56c6c;
  }
	.step-node:before {
		content: "";
		width: 10px;
		height: 10px;
		border-radius: 50%;
		display: inline-block;
		background: #3E7BFA;
		margin-right: 16px;
	}
  .step-child-node:before {
		content: "";
		width: 10px;
		height: 10px;
		border-radius: 50%;
		display: inline-block;
		background: #fff;
    border: 1px solid #3E7BFA;
		margin-right: 16px;
	}
	.step-content {
		border-left: 1px solid #3E7BFA;
		min-height: 40px;
		margin: 8px 4px;
    padding-left: 20px;
    .user {
      line-height: 20px;
      color: #1D2129;
      margin-top:16px;
      display: flex;
      .avatar {
        margin-right: 10px;
      }
    }
    .time {
      color: #B2B6BF;
      line-height: 20px;
      margin-top: 5px;
    }
    .msg {
      color: #6B7385;
      line-height: 18px;
    }
	}
}
.flowBox {
  width: 500px;
  display:inline-block;
  background: #FAFAFA;
  border: 1px solid #F2F3F5;
  border-radius: 4px;
  padding: 32px;
}
</style>
