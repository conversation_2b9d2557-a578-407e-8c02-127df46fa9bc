<template>
  <vone-echarts-card :title="formInfo.title" :name="formInfo.name">
    <div style="min-height:200px">
      <div style="margin-top: 10px;">
        <vxe-table
          ref="dasTaskTodoTable"
          class="vone-table vone-vxe-table"
          :border="true"
          resizable
          height="auto"
          show-overflow="tooltip"
          :loading="loading"
          :data="listData.records"
          :column-config="{
            minWidth:'120px',
            isHover: true
          }"
        >
          <vxe-column title="任务标题" field="name" min-width="150">
            <template #default="{ row }">
              <a @click="taskProcess(row)">
                {{ flowTitle(row)? `【${flowTitle(row)}】${row.name}` : row.name }}
              </a>
            </template>
          </vxe-column>
          <vxe-column title="节点名称" min-width="150">
            <template #default="{ row }">
              <el-tag color="#E6F9FF"><span style="color: #0588E5">{{ row.name }}</span></el-tag>
            </template>
          </vxe-column>
          <vxe-column title="接收时间" field="createTime" min-width="150">
            <template #default="{ row }">
              {{ row.createTime | dateFormat }}
            </template>
          </vxe-column>
          <vxe-column title="发起人" field="starterId" min-width="150">
            <template #default="{ row }">
              <vone-user-avatar
                :avatar-path="startDetail(row).avatarPath || ''"
                :avatar-type="startDetail(row).avatarType || ''"
                :name="startDetail(row).starterName || ''"
                height="22px"
                width="22px"
              />
            </template>
          </vxe-column>
          <vxe-column title="发起时间" field="startTime" min-width="150">
            <template #default="{ row }">
              {{ startDetail(row).startTime || '' }}
            </template>
          </vxe-column>
        </vxe-table>
      </div>
      <vone-pagination ref="pagination" small :layout="`total, prev, pager, next`" :show-custom-sizes="false" :pager-count="5" :total="listData.total" style="float: right;margin-top: 10px;" @update="getFlowList" />
    </div>

  </vone-echarts-card>
</template>
<script>
import storage from 'store'
import dayjs from 'dayjs'
// import { getFlowTodoTaskList } from '@/api/vone/workflow/task' // 已删除
export default {
  filters: {
    dateFormat(val) {
      if (!val) return ''
      return dayjs(val).format('YYYY-MM-DD HH:mm:ss')
    }
  },
  props: {
    formInfo: {
      type: Object,
      default: null
    }
  },
  data() {
    return {
      searchForm: {},
      listData: { records: [] },
      loading: false
    }
  },
  computed: {
    startDetail() {
      return function(row) {
        return row?.echoMap?.variables?.startDetail || {}
      }
    },
    flowTitle() {
      return function(row) {
        return row?.echoMap?.variables?.title || ''
      }
    }
  },
  mounted() {
    this.getFlowList()
  },
  methods: {
    async taskProcess(row) {
      const newpage = await this.$router.resolve({ path: `/task/flow/detail`, query: {
        taskId: row.id,
        processId: row.procInstId,
        type: '2'
      }})
      const winOpen = window.open(newpage.href, '_blank')
      winOpen.opener = null
    },
    // 待办任务
    async getFlowList() {
      const loginUser = storage.get('user') || {}
      const pageObj = this.$refs.pagination.exportPages()
      const params = {
        ...pageObj,
        extra: {},
        model: {
          assignee: loginUser.id,
          ...this.searchForm
        },
        order: 'descending',
        sort: 'createTime'
      }
      this.loading = true
      getFlowTodoTaskList(params).then(res => {
        this.loading = false
        if (!res.isSuccess) {
          this.$message.warning(res.msg)
          return
        }
        this.listData = res.data
      })

      // data.records = data?.records.filter(item => item.typeName != '项目立项流程' && item.typeName != '版本管理')
    }
  }
}
</script>
