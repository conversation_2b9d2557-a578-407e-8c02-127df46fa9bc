<template>
  <div style="padding: 0px 16px;">
    <vone-search-wrapper>
      <template slot="search">
        <vone-search-dynamic table-search-key="projectTodoTable" :model.sync="searchForm" :table-ref="$refs['projectTodoTable']" @getTableData="getCompletedTask">
          <el-row :gutter="16">
            <el-col :span="12">
              <el-form-item label="流程分类" prop="category">
                <vone-tree-select v-model="searchForm.category" :tree-data="typeList" search-nested placeholder="流程分类" />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="任务标题" prop="title">
                <el-input v-model="searchForm.title" clearable placeholder="请输入任务标题" />
              </el-form-item>
            </el-col>
          </el-row>
        </vone-search-dynamic>
      </template>
    </vone-search-wrapper>
    <main style="height: calc(100vh - 196rem);">
      <vxe-table
        ref="projectTodoTable"
        class="vone-vxe-table"
        border
        resizable
        height="auto"
        show-overflow="tooltip"
        :loading="tableLoading"
        :empty-render="{ name: 'empty' }"
        :data="listData.records"
        row-id="id"
        :column-config="{ minWidth:'120px' }"
      >
        <vxe-column title="任务标题" field="title" fixed>
          <template #default="{row}">
            <a type="primary" @click="taskProcess(row)">{{ row.scopeType || row.subject||row.name }}</a>
          </template>
        </vxe-column>
        <vxe-column title="节点名称" field="name" />
        <vxe-column title="优先级" field="priority">
          <template #default="{ row }">
            <span v-if="prioritMap[row.priority]">
              <i class="iconfont" :class="prioritMap[row.priority].icon" :style="{color:prioritMap[row.priority].color}" />
              <span>{{ prioritMap[row.priority].name }}</span>
            </span>
            <span v-else>
              <i class="iconfont el-icon-icon-dengji-putong2" style="color:var(--main-theme-color);" />
              <span>普通</span>
            </span>
          </template>
        </vxe-column>
        <vxe-column title="意见" field="description" />
        <vxe-column title="处理时间" field="endTime" width="160">
          <template #default="{ row }">
            {{ row.endTime | dateFormat }}
          </template>
        </vxe-column>
      </vxe-table>
    </main>
    <vone-pagination ref="pagination" :total="listData.total" @update="getCompletedTask" />

  </div>
</template>

<script>
import storage from 'store'
import dayjs from 'dayjs'
// import { getFlowDoTaskList } from '@/api/vone/workflow/task' // 已删除

export default {
  filters: {
    dateFormat(val) {
      if (!val) return ''
      return dayjs(val).format('YYYY-MM-DD HH:mm')
    }
  },
  props: {
    typeList: {
      type: Array,
      default: () => ([])
    },
    prioritMap: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      searchForm: {},
      listData: { records: [] },
      tableLoading: false
    }
  },
  mounted() {
    this.getCompletedTask()
  },
  methods: {
    taskProcess(row) {
      const description = JSON.parse(row.formData)
      this.$router.push({
        path: '/project/mytask/detail',
        query: {
          projectId: description.projectId,
          taskId: row.id,
          processId: row.procInstId,
          finish: true
        }
      })
    },

    // 获取完成任务
    async getCompletedTask() {
      this.tableLoading = true
      const loginUser = storage.get('user') || {}
      const pageObj = this.$refs.pagination?.pageObj || { current: 1, size: 20 }
      const params = {
        ...pageObj,
        extra: {},
        model: {
          assignee: loginUser.id,
          ...this.searchForm,
          processTypeIds: ['1605071942577029120'],
          categoryKey: 'project',
          findAllTree: 'true'
        }
      }
      const { data, isSuccess, msg } = await getFlowDoTaskList(params)
      if (!isSuccess) {
        this.$message.warning(msg)
        return
      }
      // data.records.map(item => {
      //   const description = JSON.parse(item.formData)
      //   item.release_type = description && description.release_type
      // })
      this.listData = data
      this.tableLoading = false
    }
  }
}
</script>

