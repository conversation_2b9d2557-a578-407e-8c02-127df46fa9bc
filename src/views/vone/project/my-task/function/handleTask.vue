<template>
  <el-dialog :visible="visible" v-bind="$attrs" width="600px" :close-on-click-modal="false" v-on="$listeners" @close="_close">
    <template slot="title">
      <template v-if="type === 1">办理</template>
      <template v-else-if="type === 2">{{ btnName }}</template>
    </template>
    <div v-loading="!approvalData">
      <el-form v-if="approvalData" ref="dialogForm" :model="ruleForm" :rules="rules">
        <div v-if="type === 1 || type === 2">
          <el-form-item v-if="!nextNode.conditionResult" label="下一节点">
            {{ nextNode.nodeName }}
            <span v-if="approvalData.isCountersign == 'true'" class="el-badge__content ml-3">会签</span>
            <span v-if="approvalData.isCallActivity == 'true'" class="el-badge__content ml-3">动态子流程</span>
          </el-form-item>
          <el-form-item v-else label="下一节点" prop="chooseNode">
            <!-- <el-checkbox-group v-model="ruleForm.chooseNode">
                <el-checkbox v-for="(item, index) in approvalData.nodeDetails" :key="index" :label="item.nodeId">{{ item.nodeName }}</el-checkbox>
            </el-checkbox-group> -->
            <el-radio-group v-model="ruleForm.chooseNode" @change="nodeChange">
              <el-radio v-for="(item, index) in approvalData.nodeDetails" :key="index" :label="item.nodeId">{{ item.nodeName }}</el-radio>
            </el-radio-group>
          </el-form-item>
          <el-form-item v-if="!isEnd" label="审批人" prop="chooseNodeUser">
            <el-checkbox-group v-model="ruleForm.chooseNodeUser">
              <el-checkbox v-for="(item, index) in userList" :key="index" :label="item.id">{{ item.name }}</el-checkbox>
            </el-checkbox-group>
          </el-form-item>
        </div>
        <el-form-item v-if="approvalData.isCallActivity == 'false'" label="任务标题" prop="taskTitle">
          <el-input v-model="ruleForm.taskTitle" />
        </el-form-item>
        <el-form-item label="优先级" prop="priority">
          <vone-icon-select
            v-model="ruleForm.priority"
            :data="prioritList"
            placeholder="请选择优先级"
            filterable
            clearable
          >
            <el-option
              v-for="item in prioritList"
              :key="item.code"
              :label="item.name"
              :value="item.code"
            >
              <div style="display: flex; align-items: center">
                <i
                  :class="`iconfont ${item.icon}`"
                  :style="{ color: item.color, fontSize: '16px', paddingRight: '6px' }"
                />
                <span>{{ item.name }}</span>
              </div>
            </el-option>
          </vone-icon-select>
        </el-form-item>
        <el-form-item label="审批意见" prop="message">
          <el-input v-model="ruleForm.message" :rows="2" type="textarea" />
        </el-form-item>
      </el-form>
    </div>
    <div slot="footer" class="dialog-footer">
      <el-button @click="_close">取消</el-button>
      <el-button type="primary" :loading="saveLoading" @click="submit">确定</el-button>
    </div>
  </el-dialog>
</template>

<script>
import storage from 'store'
// import { flowOperationHandle, getFlowNextNode } from '@/api/vone/flow/task' // 已删除
import { editFlowStatus, sendMsg } from '@/api/vone/project/index'
// import selectModel from '../../flow/components/selectModel'
import _ from 'lodash'
export default {
  components: { },
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    data: {
      type: Object,
      default: () => ({})
    },
    type: {
      type: Number,
      default: 1
    },
    btnName: {
      type: String,
      default: ''
    },
    approvalTitle: {
      type: String,
      default: ''
    },
    stepList: {
      type: Array,
      default: () => { [] }
    }
  },
  data() {
    const { taskId, processId } = this.$route.query
    return {
      taskId,
      processId,
      saveLoading: false,
      approvalData: {
        nodeDetails: []
      },
      ruleForm: {
        message: '',
        taskTitle: '',
        priority: 3,
        chooseNode: '',
        chooseNodeUser: [],
        subProcessDatas: [],
        conditionResult: undefined
      },
      userList: [],
      rules: {
        message: [{ required: true, message: '请输入审批意见' }, { pattern: /^[^\s]*$/, message: '审批意见不能输入空格' }],
        taskTitle: [{ required: true, message: '请输入任务标题' }, { pattern: /^[^\s]*$/, message: '任务标题不能输入空格' }],
        priority: [{ required: true, message: '请选择优先级' }],
        chooseNodeUser: [{ required: true, type: 'array', message: '请选择审批人' }],
        chooseNode: [{ required: true, message: '请选择下一节点' }],

        // 动态子流程
        process: [{ required: true, message: '请选择流程' }],
        user: [{ required: true, type: 'array', message: '请选择审批人' }]
      },
      prioritList: [{
        name: '最高',
        code: 5,
        icon: 'el-icon-icon-dengji-zuigao2',
        color: '#FA6A69'
      }, {
        name: '较高',
        code: 4,
        icon: 'el-icon-icon-dengji-jiaogao2',
        color: '#FA8669'
      }, {
        name: '普通',
        code: 3,
        icon: 'el-icon-icon-dengji-putong2',
        color: 'var(--main-theme-color,#3e7bfa)'
      }, {
        name: '较低',
        code: 2,
        icon: 'el-icon-icon-dengji-jiaodi2',
        color: '#5ACC5E'
      }, {
        name: '最低',
        code: 1,
        icon: 'el-icon-icon-dengji-zuidi2',
        color: '#4ECF95'
      }
      ],
      flowStatus: ''
    }
  },
  computed: {
    loginUser() {
      return storage.get('user') || {}
    },
    // 下一节点数据
    nextNode() {
      return this.approvalData?.nodeDetails?.[0] || {}
    },
    isEnd() {
      if (this.approvalData.nodeDetails.length) {
        return /^EndEvent/.test(this.approvalData.nodeDetails[0].nodeId) || /^endEvent/.test(this.approvalData.nodeDetails[0].nodeId)
      }
      return false
    }
  },
  watch: {
    visible: {
      handler(v) {
        if (v) {
          this.getFlowNextTask()
        }
      },
      immediate: true
    },
    stepList: {
      handler(v) {
        this.flowStatus = v.length == 2 ? '已立项' : '已结项'
      },
      immediate: true
    }
  },
  mounted() {

  },
  methods: {
    /**
     * 查询审批节点
     */
    async getFlowNextTask() {
      const { data, isSuccess, msg } = await getFlowNextNode({ taskId: this.taskId })
      if (!isSuccess) {
        this.$message.warning(msg)
        return
      }
      this.approvalData = data
      // 设置下一节点默认值
      this.ruleForm.chooseNode = data?.nodeDetails?.[0]?.nodeId
      this.nodeChange(this.ruleForm.chooseNode)
      // 设置子流程
      this.initProcessItem()
    },
    initProcessItem() {
      if (this.isCallActivity() && this.ruleForm.subProcessDatas.length === 0) {
        this.ruleForm.subProcessDatas = []
        this.addProcessItem()
      }
    },
    isCallActivity() {
      return this.approvalData && this.approvalData.nodeDetails.length && this.approvalData.isCallActivity == 'true'
    },
    async submit() {
      try {
        await this.$refs.dialogForm.validate()
      } catch (error) {
        this.$message.warning('请检查表单信息')
        return
      }

      const userList = []
      this.ruleForm.chooseNodeUser.map(m => {
        this.userList.map(item => {
          if (m == item.id) {
            userList.push({ id: item.id, name: item.name })
          }
        })
      })
      let nodeDetails = []
      if (this.nextNode.conditionResult) {
        this.approvalData.nodeDetails.forEach(item => {
          if (item.nodeId == this.ruleForm.chooseNode) {
            item.userDatas = []
            this.ruleForm.conditionResult = item.conditionResult

            item.userDatas = userList
            nodeDetails.push({
              userDatas: userList,
              nodeId: item.nodeId,
              nodeName: item.nodeName
            })
          }
        })
      } else {
        this.approvalData.nodeDetails.forEach(item => {
          item.userDatas = []
          delete item.defId
          delete item.candidate
          delete item.conditionResult
          item.userDatas = userList
          nodeDetails.push(item)
        })
      }
      // 未选择节点
      if (!this.ruleForm.chooseNode) {
        nodeDetails = this.approvalData.nodeDetails[0]
        nodeDetails.userDatas = nodeDetails.candidate
        delete nodeDetails.candidate
        delete nodeDetails.conditionResult
      }
      const params = {
        taskId: this.taskId,
        processInstanceId: this.processId,
        userCode: this.loginUser.id,
        userName: this.loginUser.name,
        ..._.omit(this.ruleForm, ['chooseNodeUser', 'subProcessDatas', 'conditionResult', 'chooseNode']),
        type: 'SP',
        formData: this.data || {}
      }
      if (this.type == 2) {
        Object.assign(params, {
          action: 'agree'
        })
      }
      if (this.type == 3) {
        Object.assign(params, {
          action: 'unAgree'
        })
      }
      if (this.type === 1 || this.type == 2 || this.type == 3) {
        Object.assign(params, {
          targetNodeInfos: nodeDetails,
          variables: this.ruleForm.conditionResult
        })
      }
      this.saveLoading = true
      const { isSuccess, msg } = await flowOperationHandle(params)
      this.saveLoading = false
      if (!isSuccess) {
        this.$message.warning(msg)
        return
      }
      this.$message.success(msg)
      // 获取当前节点的中文名称
      let nodeNames = ''
      this.approvalData.nodeDetails.map(item => {
        if (item.nodeId == this.ruleForm.chooseNode) {
          nodeNames = item.nodeName
        }
      })
      await editFlowStatus({
        projectId: this.$route.query.projectId,
        status: nodeNames
      }).then(res => {
        if (res.isSuccess) {
          this.$message.success('更新状态成功')
        } else {
          this.$message.warning(res.msg)
        }
      })
      // 发送站内信
      // const param = {
      //   msgDTO: {
      //     authorId: this.loginUser.id, // 发送人
      //     bizId: this.$route.query.projectId, // 业务ID
      //     bizType: 'MYTASK_NOTIFY',
      //     content: this.loginUser.name + '|创建了一个您的任务【' + this.ruleForm.taskTitle + '】【' + this.nextNode.nodeName + '】|' + this.$route.path + '|' + this.$route.query.processId + '|' + this.$route.query.projectId + '|' + this.$route.query.taskId,
      //     msgType: 'NOTIFY',
      //     title: '【我的任务】' + this.ruleForm.taskTitle
      //   },
      //   userIdList: this.ruleForm.chooseNodeUser // 接收人
      // }
      // if (this.ruleForm.chooseNodeUser.length > 0) {
      //   await sendMsg(param).then(res => {
      //     if (!res.isSuccess) {
      //       this.$message.warning(res.msg)
      //     }
      //   })
      // }
      this.$emit('update:visible', false)
      history.back()
    },
    addProcessItem() {
      this.ruleForm.subProcessDatas.push({
        id: Date.now(),
        users: [],
        nodes: [],
        selectedUser: [],
        taskTitle: ''
      })
    },
    removeProcessItem(i) {
      this.ruleForm.subProcessDatas.splice(i, 1)
    },
    changeProcess(model, process) {
      process.taskTitle = model.name
      this.getNextNodeData(model, process)
    },
    async getNextNodeData(model, process) {
      const { data, isSuccess, msg } = await getFlowNextNode({ taskId: process.taskId })
      if (!isSuccess) {
        this.$message.warning(msg)
        return
      }
      let users = []
      data.nodeDetails.forEach(node => (users = users.concat(node.candidate)))

      this.$set(process, 'nodes', data.nodeDetails)
      this.$set(process, 'users', users)
    },
    _close() {
      this.$emit('update:visible', false)
    },
    nodeChange(nodeId) {
      this.approvalData.nodeDetails.forEach(item => {
        if (item.nodeId == nodeId) {
          this.userList = item.candidate
        }
      })
    }
  }
}
</script>
<style lang="scss" scoped>
::v-deep {
  .el-card {
    position: relative;
    .el-button--danger {
      position: absolute;
      top: 10px;
      left: 10px;
    }
  }
}
</style>
