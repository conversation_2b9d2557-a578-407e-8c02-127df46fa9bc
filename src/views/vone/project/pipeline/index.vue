<template>
  <div class="sectionPageContent">
    <div class="leftSection">
      <div class="header">
        <span class="hearder-title">
          流水线
          <span class="header-total">共{{ tableData.total || '' }}条</span>
        </span>
        <div class="search">
          <el-popover v-model="visible" trigger="click" placement="bottom-start" width="300" popper-class="table-search-form org-search-form">
            <div class="search-main">
              <div class="search-header">
                <span style="flex: 1">关联流水线</span>
              </div>
              <div class="search-form">
                <el-form ref="pipelineForm" label-position="top">
                  <el-form-item label="关联流水线" prop="pipelineIds">
                    <el-select
                      v-model="pipelineForm.pipelineIds"
                      remote
                      multiple
                      clearable
                      placeholder="请查询"
                      :remote-method="remoteMethod"
                      loading-text="正在查询..."
                      no-match-text="暂未查询到匹配数据,请重新输入"
                      :loading="loading"
                      popper-class="pipeline"
                    >
                      <el-option v-for="item in pipelineData" :key="item.id" :label="item.name" :value="item.id" />
                    </el-select>
                  </el-form-item>
                </el-form>
              </div>
              <div class="footer org-footer">
                <el-button plain @click="cancel">取消</el-button>
                <el-button type="primary" :loading="btnLoading" @click="savePipeline">确定</el-button>
              </div>
            </div>
            <span slot="reference">
              <el-tooltip class="item" effect="dark" content="关联流水线" placement="top">
                <i :class="['iconfont', 'iconfont el-icon-edit-relate', visible ? 'active' : '']" style="margin-left: 12px;cursor: pointer;" />
              </el-tooltip>
            </span>
          </el-popover>
          <el-dropdown trigger="click" class="add-pipeline" @command="add">
            <el-tooltip class="item" effect="dark" content="新增流水线" placement="top">
              <i style="margin-left: 8px" class="iconfont el-icon-tips-plus" />
            </el-tooltip>
            <el-dropdown-menu slot="dropdown" style="width: 120px">
              <el-dropdown-item command="CUSTOM">快捷型</el-dropdown-item>
              <el-dropdown-item command="SCHEDULE">调度型</el-dropdown-item>
            </el-dropdown-menu>
          </el-dropdown>
        </div>
      </div>
      <div v-infinite-scroll="load" class="cardContent">
        <vone-empty v-if="tableData.records.length == 0" />
        <div v-else>
          <el-card v-for="item in tableData.records" :key="item.id" shadow="hover" :class="['left-small-card', item.id == pipelineId ? 'is-active' : '']" @click.native="changePipeline(item)">
            <div class="small-card-title">
              <div class="small-card-title-left">
                <svg-icon v-if="item.mode.code == 'CUSTOM'" icon-class="pipeline1" />
                <svg-icon v-if="item.mode.code == 'SCHEDULE'" icon-class="pipeline2" />
                <span>{{ item.name }}</span>
              </div>
              <div @click.stop>
                <el-dropdown trigger="click">
                  <i class="iconfont el-icon-application-more" style="cursor: pointer" />
                  <el-dropdown-menu slot="dropdown">
                    <!-- <el-dropdown-item icon="iconfont el-icon-application-edit" @click.native="editPipeline(item)">
                      编辑
                    </el-dropdown-item> -->
                    <el-dropdown-item icon="iconfont el-icon-application-delete" @click.native="delPipeline(item)">
                      删除
                    </el-dropdown-item>
                    <el-dropdown-item icon="iconfont el-icon-edit-unrelate" @click.native="cancelRelevance(item)">
                      取消关联
                    </el-dropdown-item>
                  </el-dropdown-menu>
                </el-dropdown>
              </div>
            </div>
            <div class="small-card-desc">
              {{ item.description }}
            </div>
          </el-card>
        </div>
      </div>
    </div>
    <div class="rightSection">
      <pipelinetab v-if="pipelineId" :pipeline-id="pipelineId" />
      <div v-else class="empty_wrap">
        <div class="pic" />
        <div class="title">流水线</div>
        <span class="info">项目下流水线的关联和新增</span>
      </div>
    </div>
    <add-pipeline v-if="addPipelineVisible" :mode="mode" :visible.sync="addPipelineVisible" @success="resetTable" @changePipeline="changePipeline" />
  </div>
</template>
<script>
import Pipelinetab from './pipeline/pipelinetab.vue'
import AddPipeline from './add-pipeline.vue'
import { cloneDeep, debounce } from 'lodash'
import { apiBuniessPipelineJobs, apiDeleteBuniessPipelineJobs } from '@/api/vone/pipeline/index'
import { getPipelineToProject, relevancePipelineForProject, cancelRelevancePipeline } from '@/api/vone/project/pipeline/index'
export default {
  components: {
    Pipelinetab,
    AddPipeline
  },
  data() {
    return {
      loading: false,
      btnLoading: false,
      treeLoading: false,
      pageLoading: false,
      tableData: {
        records: []
      },
      visible: false,
      pipelineForm: {
        pipelineIds: []
      },
      initPipelineData: [],
      pipelineData: [],
      pipelineId: '',
      current: 0,
      addPipelineVisible: false,
      mode: 'CUSTOM'
    }
  },
  async mounted() {
    this.getPipelineToProject()
    // await this.getTableList()
  },
  methods: {
    // 切换流水线
    changePipeline(data) {
      this.$router.replace({ query: {}})
      this.pipelineId = null
      const loading = this.$loading({
        lock: true,
        target: document.querySelector('.rightSection')
      })
      this.$nextTick(() => {
        if (timer) clearTimeout(timer)
        const timer = setTimeout(() => {
          this.pipelineId = data.id
          loading.close()
        }, 300)
      })
    },
    // 查询当前登录用户可关联的流水线信息
    getPipelineToProject() {
      getPipelineToProject(this.$route.params.id).then(res => {
        if (res.isSuccess) {
          this.initPipelineData = cloneDeep(res.data)
          this.pipelineData = res.data
        }
      })
    },
    remoteMethod: debounce(function(query) {
      if (query == '') {
        this.pipelineData = this.initPipelineData
        return
      }
      this.loading = true
      const params = {
        name: query
      }
      getPipelineToProject(this.$route.params.id, params).then(res => {
        this.loading = false
        if (res.isSuccess) {
          this.pipelineData = res.data
        }
      }).catch(() => { this.loading = false })
    }, 1000),
    savePipeline() {
      this.btnLoading = true
      relevancePipelineForProject(this.$route.params.id, this.pipelineForm.pipelineIds).then(res => {
        this.btnLoading = false
        if (res.isSuccess) {
          this.$message.success('关联成功')
          this.resetTable()
        } else {
          this.$message.warning(res.msg)
        }
        this.cancel()
      })
    },
    // 取消关联
    cancelRelevance(row) {
      this.$confirm('是否取消关联当前流水线?', '取消关联', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        customClass: 'delConfirm',
        showClose: false,
        type: 'warning'
      }).then(() => {
        cancelRelevancePipeline(this.$route.params.id, [row.id]).then((res) => {
          if (res.isSuccess) {
            this.$message.success('取消关联成功')
            this.resetTable()
          } else {
            this.$message.warning(res.msg)
          }
        })
      })
    },
    // 重置左侧列表
    async resetTable(data) {
      this.$router.replace({ query: {}})
      this.current = 1
      this.pipelineId = null
      this.tableData.records = []
      this.projectId = null
      await this.getTableList()
      if (data) {
        this.$nextTick(() => {
          this.changePipeline(data)
        })
      }
    },
    cancel() {
      this.pipelineForm = {
        pipelineIds: []
      }
      this.visible = false
    },
    editPipeline() {

    },
    async delPipeline(item) {
      await this.$confirm('确定删除该流水线吗?', '删除', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        customClass: 'delConfirm',
        showClose: false,
        type: 'warning'
      })
      const { isSuccess, msg } = await apiDeleteBuniessPipelineJobs([item.id])
      if (!isSuccess) {
        this.$message.warning(msg)
        return
      }
      this.$message.success(msg)
      this.resetTable()
    },
    add(command) {
      this.addPipelineVisible = true
      this.mode = command
    },
    load() {
      const total = this.tableData?.total
      if (total && this.current * 10 >= total) return
      this.current = this.current + 1
      this.$nextTick(() => {
        this.getTableList()
      })
    },
    async getTableList() {
      const tableParams = {
        'sort': 'createTime',
        'order': 'descending',
        'current': this.current,
        'size': 10,
        'extra': {},
        'model': {
          'projectId': this.$route.params.id
        }
      }
      this.pageLoading = true
      const res = await apiBuniessPipelineJobs(tableParams)
      if (!res.isSuccess) {
        this.$message.warning(res.msg)
        return
      }
      this.pageLoading = false
      if (!this.tableData.records) {
        this.tableData = res.data
      } else {
        const oldData = cloneDeep(this.tableData.records)
        this.tableData = res.data
        this.tableData.records = [...oldData, ...res.data.records]
      }
      if (this.$route.query?.pipelineId) {
        this.$nextTick(() => {
          this.pipelineId = this.$route.query?.pipelineId
        })
        return
      }
      if (!this.projectId) this.pipelineId = res.data?.records[0]?.id || null
    }

  }
}
</script>
<style lang="scss" scoped>
@import "~@/styles/variables.scss";
.rightSection {
  background: none;
  padding: 0;
  box-shadow: none;
}
.org-search-form .search-main{
  .search-header {
    padding: 12px 20px;
    font-weight: 500;
    color: var(--font-main-color);
    border-bottom: 1px solid var(--solid-border-color);
  }
  .search-form {
    padding: 16px;
    .el-form-item {
      width: 100%;
    }
  }
  .org-footer {
    text-align: right;
    padding: 12px 20px;
    border-top: 1px solid var(--disabled-bg-color);
  }
}

.leftSection {
	width: 240px;
  height: calc(100vh - 48px - 20px);
	.header {
		padding: 0px 16px;
		height: 48px;
		line-height: 48px;
		border-bottom: 1px solid var(--solid-border-color);
		display:flex;
		.hearder-title {
			color: var(--font-main-color);
			font-size: 16px;
			font-weight: 500;
			flex: 1;
		}
    .header-total {
      font-size: 12px;
      color: #777F8E;
    }
		.search {
			display: inline-block;
			.iconfont {
				cursor: pointer;
				color: var(--font-second-color);
			}
			.iconfont:hover {
				color: var(--main-theme-color);
			}
			.iconfont.active {
				color: var(--main-theme-color);
			}
		}
	}
	.cardContent {
		padding:16px 16px 0;
    height: calc(100vh - 48px - 48px - 50px);
		overflow-y: overlay;
    .left-small-card {
      margin-bottom: 16px;
      cursor: pointer;
    }
  }
}
.empty_wrap {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
  color: var(--main-font-color);
  .pic {
    height: 168px;
    width: 240px;
    // background: url("../../../../assets/project/pipeline-empty.png") no-repeat; // 已删除
    background-size: 100% 100%;
  }
  .title {
    font-weight: 600;
    font-size: 16px;
  }
  .info {
    font-weight: 400;
    font-size: 14px;
  }
}
</style>
